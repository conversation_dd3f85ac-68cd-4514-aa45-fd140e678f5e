from fastapi import Depends, FastAPI, HTTPException
from contextlib import asynccontextmanager
from database import init_db, async_init_db
import models
import routers.projects
import routers.tasks
import routers.users
import uvicorn
import asyncio
from fastapi.middleware.cors import CORSMiddleware
from middleware.logging_middleware import LoggingMiddleware, RequestContextMiddleware
from logger_config import app_logger
import os

# 添加JWT配置
SECRET_KEY = "YOUR_SECRET_KEY_HERE"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

app = FastAPI(title="项目管理系统API", version="1.0.0")

# 添加日志中间件（注意顺序很重要）
app.add_middleware(RequestContextMiddleware)
app.add_middleware(LoggingMiddleware, exclude_paths=["/docs", "/redoc", "/openapi.json", "/favicon.ico"])

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    app_logger.info("应用程序启动中...")
    try:
        await async_init_db()
        app_logger.info("数据库初始化完成")
        app_logger.info("应用程序启动成功")
    except Exception as e:
        app_logger.error(f"应用程序启动失败: {str(e)}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    app_logger.info("应用程序正在关闭...")

# 注册路由
app.include_router(routers.projects.router)
app.include_router(routers.tasks.router)
app.include_router(routers.users.router)

app_logger.info("路由注册完成")

if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)